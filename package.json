{"name": "@dohoder/source", "version": "0.0.0", "license": "MIT", "scripts": {"start:admin": "nx dev dohoder-admin", "build:admin": "nx build dohoder-admin", "test:admin": "nx test dohoder-admin", "start:mobile": "cd dohoder-mobile && npx expo start", "start:mobile:web": "cd dohoder-mobile && npx expo start --web", "start:mobile:ios": "cd dohoder-mobile && npx expo start --ios", "start:mobile:android": "cd dohoder-mobile && npx expo start --android", "build:mobile": "cd dohoder-mobile && npx expo export", "build:mobile:export": "cd dohoder-mobile && npx expo export", "prebuild:mobile": "cd dohoder-mobile && npx expo prebuild", "install:mobile": "cd dohoder-mobile && npx expo install", "test:mobile": "nx test dohoder-mobile", "docker:build": "docker compose up -d --build", "nx:reset": "nx reset"}, "private": true, "devDependencies": {"@eslint/compat": "~1.1.1", "@eslint/eslintrc": "~2.1.1", "@eslint/js": "~9.8.0", "@expo/cli": "~54.0.10", "@next/eslint-plugin-next": "~15.2.4", "@nx/cypress": "21.6.2", "@nx/eslint": "21.6.2", "@nx/eslint-plugin": "21.6.2", "@nx/expo": "21.6.2", "@nx/express": "^21.6.2", "@nx/jest": "21.6.2", "@nx/js": "^21.6.2", "@nx/next": "^21.6.2", "@nx/node": "21.6.2", "@nx/react": "^21.6.2", "@nx/web": "21.6.2", "@nx/webpack": "21.6.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@svgr/webpack": "^8.1.0", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "^1.13.5", "@swc/helpers": "~0.5.11", "@swc/jest": "~0.2.36", "@swc/plugin-styled-components": "^1.5.67", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@testing-library/react-native": "~13.2.0", "@types/express": "^4.17.21", "@types/jest": "~29.5.1", "@types/node": "20.19.9", "@types/react": "^19.1.17", "@types/react-dom": "^19.1.11", "@types/react-is": "19.0.0", "@types/styled-components": "5.1.26", "babel-jest": "~29.7.0", "babel-plugin-styled-components": "1.10.7", "babel-preset-expo": "~54.0.0", "cypress": "^14.2.1", "eslint": "^9.8.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^10.0.0", "eslint-plugin-cypress": "^3.5.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "~29.7.0", "jest-environment-jsdom": "~29.7.0", "jest-environment-node": "^29.7.0", "jest-expo": "~54.0.12", "jest-util": "~29.7.0", "jsonc-eslint-parser": "^2.1.0", "metro-config": "~0.82.4", "metro-resolver": "~0.82.4", "nx": "21.6.2", "prettier": "^2.6.2", "react-refresh": "^0.14.0", "supabase": "^2.47.2", "ts-jest": "~29.1.1", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.9.2", "typescript-eslint": "^8.40.0", "webpack-cli": "^5.1.4"}, "workspaces": ["dohoder-admin", "dohoder-mobile", "calendar-sync-api", "calendar-sync-api-e2e", "libs", "libs/*"], "dependencies": {"@expo/metro-runtime": "~6.1.2", "@react-native-async-storage/async-storage": "2.2.0", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.58.0", "@tanstack/query-sync-storage-persister": "^5.90.2", "@tanstack/react-query": "^5.90.2", "axios": "^1.6.0", "expo": "^54.0.12", "expo-splash-screen": "~31.0.10", "expo-status-bar": "~3.0.8", "expo-system-ui": "~6.0.7", "express": "^4.21.2", "next": "~15.2.4", "react": "19.1.0", "react-dom": "19.1.0", "react-is": "19.1.0", "react-native": "0.81.4", "react-native-svg": "15.12.1", "react-native-svg-transformer": "~1.5.1", "react-native-url-polyfill": "^3.0.0", "react-native-web": "^0.21.0", "styled-components": "5.3.6"}, "packageManager": "yarn@3.2.1+sha512.2e8753234dc01d8cc7ed64c98e459a382c68cf239ce06102b2bec91cbbdf383ba520010e85df0f70fc400648d0706ac5bf37b59d99e3488c44a5436b50bf4204"}