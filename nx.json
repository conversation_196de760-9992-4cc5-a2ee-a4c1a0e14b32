{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/cypress/**/*", "!{projectRoot}/**/*.cy.[jt]s?(x)", "!{projectRoot}/cypress.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": []}, "nxCloudId": "68dd76517d14111c79d7ad6a", "plugins": [{"plugin": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"plugin": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "devTargetName": "dev", "serveStaticTargetName": "serve-static", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/cypress/plugin", "options": {"targetName": "e2e", "openTargetName": "open-cypress", "componentTestingTargetName": "component-test", "ciTargetName": "e2e-ci"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}, "exclude": ["calendar-sync-api-e2e/**/*"]}, {"plugin": "@nx/expo/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "prebuildTargetName": "prebuild", "serveTargetName": "serve", "installTargetName": "install", "exportTargetName": "export", "submitTargetName": "submit", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps", "serveStaticTargetName": "serve-static"}}], "targetDefaults": {"test": {"dependsOn": ["^build"]}, "@nx/js:swc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "generators": {"@nx/next": {"application": {"style": "styled-components", "linter": "eslint"}}, "@nx/react": {"library": {"unitTestRunner": "jest"}}}}