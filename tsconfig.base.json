{"compilerOptions": {"composite": true, "declarationMap": true, "emitDeclarationOnly": true, "importHelpers": true, "isolatedModules": true, "lib": ["es2022"], "module": "nodenext", "moduleResolution": "nodenext", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": true, "skipLibCheck": true, "strict": true, "target": "es2022", "customConditions": ["@dohoder/source"], "paths": {"@dohoder/domain": ["libs/domain/src/index.ts"], "@dohoder/data-access": ["libs/data-access/src/index.ts"], "@dohoder/ui": ["libs/ui/src/index.ts"], "@dohoder/util": ["libs/util/src/index.ts"]}}}