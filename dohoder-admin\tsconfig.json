{"extends": "../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "noEmit": true, "emitDeclarationOnly": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "types": ["jest", "node"], "outDir": "dist", "rootDir": "."}, "include": ["app/**/*.ts", "app/**/*.tsx", "app/**/*.js", "app/**/*.jsx", "../dohoder-admin/.next/types/**/*.ts", "../dist/dohoder-admin/.next/types/**/*.ts", "next-env.d.ts"], "exclude": ["out-tsc", "dist", "node_modules", "jest.config.ts", "app/**/*.spec.ts", "app/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.ts", ".next", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"]}