{"extends": "../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "noEmit": true, "emitDeclarationOnly": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "types": ["jest", "node"], "outDir": "dist", "rootDir": "."}, "include": ["../dist/dohoder-admin/.next/types/**/*.ts", "../dohoder-admin/.next/types/**/*.ts", "app/**/*.js", "app/**/*.jsx", "app/**/*.ts", "app/**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["out-tsc", "dist", "node_modules", "jest.config.ts", "app/**/*.spec.ts", "app/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.ts", ".next", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"]}