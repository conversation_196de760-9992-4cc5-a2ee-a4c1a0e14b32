{"name": "@dohoder/calendar-sync-api", "version": "0.0.1", "private": true, "nx": {"targets": {"prune-lockfile": {"dependsOn": ["build"], "cache": true, "executor": "@nx/js:prune-lockfile", "outputs": ["{workspaceRoot}/calendar-sync-api/dist/package.json", "{workspaceRoot}/calendar-sync-api/dist/package-lock.json"], "options": {"buildTarget": "build"}}, "copy-workspace-modules": {"dependsOn": ["build"], "cache": true, "outputs": ["{workspaceRoot}/calendar-sync-api/dist/workspace_modules"], "executor": "@nx/js:copy-workspace-modules", "options": {"buildTarget": "build"}}, "prune": {"dependsOn": ["prune-lockfile", "copy-workspace-modules"], "executor": "nx:noop"}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@dohoder/calendar-sync-api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@dohoder/calendar-sync-api:build:development"}, "production": {"buildTarget": "@dohoder/calendar-sync-api:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}, "dependencies": {"express": "^4.21.2"}}