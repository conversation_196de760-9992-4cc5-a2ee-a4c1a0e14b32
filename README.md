# 🎯 DOHODER Monorepo

<div align="center">
  <img src="https://raw.githubusercontent.com/nrwl/nx/master/images/nx-logo.png" width="60" alt="Nx logo">
  <h3>Powered by Nx Workspace</h3>
</div>

## 🚀 Prehľad

Tento repozitár je **monolitické úložisko** spravované pomocou **NX**, ktoré združuje všetky aplikácie a zdieľané knižnice pre projekt **DOHODER**.

Projekt je zameraný na **Mobile-First** riešenie s React Native/Expo mobilnou aplikáciou a Next.js administráciou.

### 🏗️ Architektúra

- **📱 Mobile App**: React Native s Expo (hlavná aplikácia) - používa priamo Expo CLI
- **💻 Admin Panel**: Next.js s App Router (administrácia) - spravované cez Nx
- **🔧 Monorepo**: Nx workspace pre efektívne spravovanie kódu
- **📦 Package Manager**: NPM s Yarn 3.2.1 support

---

## ⚙️ Rýchla Inicializácia (Setup)

### 1. Predpoklady

Uistite sa, že máte nainštalované:

- **Node.js** (v18+)
- **NPM** alebo **Yarn**
- **Git**

### 2. Inštalácia Závislostí

```bash
# Klonujte repozitár
git clone <repository-url>
cd dohoder

# Nainštalujte závislosti
npm install

# Alebo s Yarn
yarn install
```

### 3. Konfigurácia Prostredia

Vytvorte súbor `.env` v koreňovom adresári:

```bash
# .env (Vytvorte v koreni projektu)
SUPABASE_URL="https://[VASE_SUPABASE_ID].supabase.co"
SUPABASE_ANON_KEY="[VASE_ANON_KEY]"
```

---

## 🚀 Spúšťacie Skripty

### 📱 Mobile Aplikácia (Expo/React Native)

> **Poznámka**: Mobile aplikácia používa priamo **Expo CLI** spustené z adresára projektu pre optimálnu kompatibilitu.

#### Development Servery:

```bash
# Základný Expo development server (s QR kódom pre Expo Go)
npm run start:mobile
# Ekvivalent: cd dohoder-mobile && npx expo start

# Spustenie vo web prehliadači
npm run start:mobile:web
# Ekvivalent: cd dohoder-mobile && npx expo start --web

# Spustenie na iOS simulátore/zariadení
npm run start:mobile:ios
# Ekvivalent: cd dohoder-mobile && npx expo start --ios

# Spustenie na Android emulátore/zariadení
npm run start:mobile:android
# Ekvivalent: cd dohoder-mobile && npx expo start --android
```

#### Build a Export:

```bash
# Export aplikácie (pre web deployment alebo lokálne testovanie)
npm run build:mobile
# Ekvivalent: cd dohoder-mobile && npx expo export

# Export aplikácie pre statické hostovanie
npm run build:mobile:export
# Ekvivalent: cd dohoder-mobile && npx expo export

# Vygenerovanie natívnych iOS/Android projektov (pre custom native code)
npm run prebuild:mobile
# Ekvivalent: cd dohoder-mobile && npx expo prebuild

# Inštalácia Expo kompatibilných závislostí
npm run install:mobile
# Ekvivalent: cd dohoder-mobile && npx expo install
```

#### Testing:

```bash
# Spustenie testov pre mobile aplikáciu
npm run test:mobile
```

### 💻 Admin Aplikácia (Next.js)

```bash
# Spustenie admin aplikácie v development móde
npm run start:admin

# Vytvorenie produkčného buildu admin aplikácie
npm run build:admin

# Spustenie testov pre admin aplikáciu
npm run test:admin
```

### 🐳 Ostatné Utility Skripty

```bash
# Docker Compose build
npm run docker:build

# Vyčistenie Nx cache
npm run nx:reset
```

---

## 🏗️ Adresárová Štruktúra

```
/DOHODER (Koreňový Priečinok)
├── dohoder-mobile/             # 📱 Expo/React Native App (Hlavná Aplikácia)
│   ├── app.json               # Expo konfigurácia
│   ├── assets/                # Obrázky, fonty, ikony
│   ├── src/                   # Zdrojový kód aplikácie
│   ├── metro.config.js        # Metro bundler konfigurácia
│   └── eas.json              # EAS Build konfigurácia
│
├── dohoder-admin/             # 💻 Next.js App (Administrácia)
│   ├── app/                   # Next.js App Router
│   ├── public/                # Statické súbory
│   ├── next.config.js         # Next.js konfigurácia
│   └── package.json           # Admin závislosti
│
├── packages/                  # 📦 Zdieľané knižnice (budúce)
│   └── (prázdne - pripravené na rozšírenie)
│
├── tools/                     # 🔧 Build nástroje a skripty
│   └── scripts/               # Pomocné skripty
│
├── nx.json                    # 🎯 Centrálna konfigurácia NX
├── package.json               # 📋 Root závislosti a skripty
├── tsconfig.base.json         # 📝 TypeScript base konfigurácia
└── README.md                  # 📖 Tento súbor
```

---

## 🎯 Najčastejšie Používané Príkazy

### 🚀 Quick Start (Najrýchlejší spôsob)

```bash
# 1. Nainštalujte závislosti
npm install

# 2. Spustite mobile aplikáciu
npm run start:mobile

# 3. V novom termináli spustite admin
npm run start:admin
```

### 📱 Mobile Development

```bash
# Spustenie Expo dev servera s automatickým reloadom
npm run start:mobile

# Pre iOS development (vyžaduje macOS + Xcode alebo Expo Go app)
npm run start:mobile:ios

# Pre Android development (vyžaduje Android Studio alebo Expo Go app)
npm run start:mobile:android

# Web verzia (pre rýchle testovanie v prehliadači)
npm run start:mobile:web
```

> **Tip**: Pre najrýchlejšie testovanie použite **Expo Go** aplikáciu na vašom telefóne a naskenujte QR kód z terminálu.

### 🔧 Development Tools

```bash
# Zobrazenie project graph
npx nx graph

# Spustenie konkrétneho targetu
npx nx <target> <project-name>

# Príklad: Build mobile aplikácie
npx nx build dohoder-mobile

# Spustenie testov
npx nx test dohoder-mobile
npx nx test dohoder-admin
```

---

## 📋 Technológie a Nástroje

### 🏗️ Core Stack

| Technológia      | Verzia  | Účel                        |
| ---------------- | ------- | --------------------------- |
| **Nx**           | 21.6.2  | Monorepo management         |
| **React**        | 19.1.0  | UI knižnica                 |
| **React Native** | 0.81.4  | Mobile development          |
| **Expo**         | 54.0.12 | Mobile development platform |
| **Next.js**      | 15.2.4  | Web framework pre admin     |
| **TypeScript**   | 5.9.2   | Type safety                 |
| **Jest**         | 29.7.0  | Testing framework           |

### 🔧 Development Tools

- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Cypress** - E2E testing
- **Metro** - React Native bundler
- **SWC** - Fast TypeScript/JavaScript compiler

---

## 🚀 Deployment

### 📱 Mobile App (Expo)

```bash
# Export aplikácie (vytvorí optimalizované súbory)
npm run build:mobile
# Alebo priamo: cd dohoder-mobile && npx expo export

# Export pre web deployment alebo statické hostovanie
npm run build:mobile:export
# Alebo priamo: cd dohoder-mobile && npx expo export

# Prebuild - vygeneruje natívne iOS/Android projekty
npm run prebuild:mobile
# Alebo priamo: cd dohoder-mobile && npx expo prebuild
```

> **Poznámka**: Pre produkčné buildy odporúčame použiť **EAS Build** službu od Expo:
>
> ```bash
> cd dohoder-mobile && npx eas build --platform all
> ```

### 💻 Admin Panel (Next.js)

```bash
# Produkčný build
npm run build:admin

# Spustenie produkčnej verzie
npm run start:admin
```

---

## 🧪 Testing

```bash
# Spustenie všetkých testov
npx nx run-many --target=test

# Testy pre konkrétny projekt
npm run test:mobile
npm run test:admin
```

---

## 🔗 Užitočné Odkazy

### 📚 Dokumentácia

- [Nx Documentation](https://nx.dev)
- [Expo Documentation](https://docs.expo.dev)
- [Next.js Documentation](https://nextjs.org/docs)
- [React Native Documentation](https://reactnative.dev)

### 🛠️ Nx Tools

- [Nx Console](https://nx.dev/getting-started/editor-setup) - VSCode/IntelliJ extension
- [Nx Cloud](https://cloud.nx.app) - Remote caching a CI/CD

### 🏃‍♂️ Quick Commands

```bash
# Zobrazenie všetkých projektov
npx nx show projects

# Zobrazenie project graph
npx nx graph

# Generovanie novej knižnice
npx nx g @nx/js:lib packages/my-lib

# Reset Nx cache
npm run nx:reset
```

---

## 👥 Tím a Podpora

Pre otázky a podporu kontaktujte vývojový tím alebo vytvorte issue v tomto repozitári.

**Happy Coding! 🚀**
