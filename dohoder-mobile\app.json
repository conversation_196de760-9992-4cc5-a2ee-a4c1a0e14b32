{"expo": {"name": "Dohoder", "slug": "dohoder", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "dohoder-mobile", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "extra": {"eas": {"projectId": "25348b45-0563-4e67-bec5-1ab7b5457a1a"}}, "owner": "spacer33"}}