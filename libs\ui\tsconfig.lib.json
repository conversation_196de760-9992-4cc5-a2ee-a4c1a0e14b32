{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "types": ["node", "@nx/react/typings/cssmodule.d.ts", "@nx/react/typings/image.d.ts"], "rootDir": "src", "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "tsBuildInfoFile": "dist/tsconfig.lib.tsbuildinfo"}, "exclude": ["out-tsc", "dist", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"]}